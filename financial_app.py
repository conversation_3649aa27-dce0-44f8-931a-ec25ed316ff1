import tushare as ts
import pandas as pd
import streamlit as st
from datetime import datetime
import warnings
import time
warnings.filterwarnings('ignore')

# 定义变量字典
variables_dict = {
    "ts_code": "TS股票代码",
    "ann_date": "公告日期",
    "f_ann_date": "实际公告日期",
    "end_date": "报告期",
    "report_type": "报告类型",
    "comp_type": "公司类型(1一般工商业2银行3保险4证券)",
    "end_type": "报告期类型",
    "basic_eps": "基本每股收益",
    "diluted_eps": "稀释每股收益",
    "total_revenue": "营业总收入",
    "revenue": "营业收入",
    "int_income": "利息收入",
    "prem_earned": "已赚保费",
    "comm_income": "手续费及佣金收入",
    "n_commis_income": "手续费及佣金净收入",
    "n_oth_income": "其他经营净收益",
    "n_oth_b_income": "加:其他业务净收益",
    "prem_income": "保险业务收入",
    "out_prem": "减:分出保费",
    "une_prem_reser": "提取未到期责任准备金",
    "reins_income": "其中:分保费收入",
    "n_sec_tb_income": "代理买卖证券业务净收入",
    "n_sec_uw_income": "证券承销业务净收入",
    "n_asset_mg_income": "受托客户资产管理业务净收入",
    "oth_b_income": "其他业务收入",
    "fv_value_chg_gain": "加:公允价值变动净收益",
    "invest_income": "加:投资净收益",
    "ass_invest_income": "其中:对联营企业和合营企业的投资收益",
    "forex_gain": "加:汇兑净收益",
    "total_cogs": "营业总成本",
    "oper_cost": "减:营业成本",
    "int_exp": "减:利息支出",
    "comm_exp": "减:手续费及佣金支出",
    "biz_tax_surchg": "减:营业税金及附加",
    "sell_exp": "减:销售费用",
    "admin_exp": "减:管理费用",
    "fin_exp": "减:财务费用",
    "assets_impair_loss": "减:资产减值损失",
    "prem_refund": "退保金",
    "compens_payout": "赔付总支出",
    "reser_insur_liab": "提取保险责任准备金",
    "div_payt": "保户红利支出",
    "reins_exp": "分保费用",
    "oper_exp": "营业支出",
    "compens_payout_refu": "减:摊回赔付支出",
    "insur_reser_refu": "减:摊回保险责任准备金",
    "reins_cost_refund": "减:摊回分保费用",
    "other_bus_cost": "其他业务成本",
    "operate_profit": "营业利润",
    "non_oper_income": "加:营业外收入",
    "non_oper_exp": "减:营业外支出",
    "nca_disploss": "其中:减:非流动资产处置净损失",
    "total_profit": "利润总额",
    "income_tax": "所得税费用",
    "n_income": "净利润(含少数股东损益)",
    "n_income_attr_p": "净利润(不含少数股东损益)",
    "minority_gain": "少数股东损益",
    "oth_compr_income": "其他综合收益",
    "t_compr_income": "综合收益总额",
    "compr_inc_attr_p": "归属于母公司(或股东)的综合收益总额",
    "compr_inc_attr_m_s": "归属于少数股东的综合收益总额",
    "ebit": "息税前利润",
    "ebitda": "息税折旧摊销前利润",
    "insurance_exp": "保险业务支出",
    "undist_profit": "年初未分配利润",
    "distable_profit": "可分配利润",
    "rd_exp": "研发费用",
    "fin_exp_int_exp": "财务费用:利息费用",
    "fin_exp_int_inc": "财务费用:利息收入",
    "credit_impa_loss": "信用减值损失",
    "net_expo_hedging_benefits": "净敞口套期收益",
    "oth_impair_loss_assets": "其他资产减值损失",
    "total_opcost": "营业总成本（二）",
    "amodcost_fin_assets": "以摊余成本计量的金融资产终止确认收益",
    "oth_income": "其他收益",
    "asset_disp_income": "资产处置收益",
    "continued_net_profit": "持续经营净利润",
    "end_net_profit": "终止经营净利润",
    "update_flag": "更新标识"
}

variables_dict_2 = {
    "ts_code": "TS股票代码",
    "ann_date": "公告日期",
    "f_ann_date": "实际公告日期",
    "end_date": "报告期",
    "report_type": "报表类型",
    "comp_type": "公司类型(1一般工商业2银行3保险4证券)",
    "end_type": "报告期类型",
    "total_share": "期末总股本",
    "cap_rese": "资本公积金",
    "undistr_porfit": "未分配利润",
    "surplus_rese": "盈余公积金",
    "special_rese": "专项储备",
    "money_cap": "货币资金",
    "trad_asset": "交易性金融资产",
    "notes_receiv": "应收票据",
    "accounts_receiv": "应收账款",
    "oth_receiv": "其他应收款",
    "prepayment": "预付款项",
    "div_receiv": "应收股利",
    "int_receiv": "应收利息",
    "inventories": "存货",
    "amor_exp": "待摊费用",
    "nca_within_1y": "一年内到期的非流动资产",
    "sett_rsrv": "结算备付金",
    "loanto_oth_bank_fi": "拆出资金",
    "premium_receiv": "应收保费",
    "reinsur_receiv": "应收分保账款",
    "reinsur_res_receiv": "应收分保合同准备金",
    "pur_resale_fa": "买入返售金融资产",
    "oth_cur_assets": "其他流动资产",
    "total_cur_assets": "流动资产合计",
    "fa_avail_for_sale": "可供出售金融资产",
    "htm_invest": "持有至到期投资",
    "lt_eqt_invest": "长期股权投资",
    "invest_real_estate": "投资性房地产",
    "time_deposits": "定期存款",
    "oth_assets": "其他资产",
    "lt_rec": "长期应收款",
    "fix_assets": "固定资产",
    "cip": "在建工程",
    "const_materials": "工程物资",
    "fixed_assets_disp": "固定资产清理",
    "produc_bio_assets": "生产性生物资产",
    "oil_and_gas_assets": "油气资产",
    "intan_assets": "无形资产",
    "r_and_d": "研发支出",
    "goodwill": "商誉",
    "lt_amor_exp": "长期待摊费用",
    "defer_tax_assets": "递延所得税资产",
    "decr_in_disbur": "发放贷款及垫款",
    "oth_nca": "其他非流动资产",
    "total_nca": "非流动资产合计",
    "total_assets": "资产总计",
    "lt_borr": "长期借款",
    "st_borr": "短期借款",
    "cb_borr": "向中央银行借款",
    "depos_ib_deposits": "吸收存款及同业存放",
    "loan_oth_bank": "拆入资金",
    "trading_fl": "交易性金融负债",
    "notes_payable": "应付票据",
    "acct_payable": "应付账款",
    "adv_receipts": "预收款项",
    "sold_for_repur_fa": "卖出回购金融资产款",
    "comm_payable": "应付手续费及佣金",
    "payroll_payable": "应付职工薪酬",
    "taxes_payable": "应交税费",
    "int_payable": "应付利息",
    "div_payable": "应付股利",
    "oth_payable": "其他应付款",
    "acc_exp": "预提费用",
    "deferred_inc": "递延收益",
    "st_bonds_payable": "应付短期债券",
    "payable_to_reinsurer": "应付分保账款",
    "rsrv_insur_cont": "保险合同准备金",
    "acting_trading_sec": "代理买卖证券款",
    "acting_uw_sec": "代理承销证券款",
    "non_cur_liab_due_1y": "一年内到期的非流动负债",
    "oth_cur_liab": "其他流动负债",
    "total_cur_liab": "流动负债合计",
    "bond_payable": "应付债券",
    "lt_payable": "长期应付款",
    "specific_payables": "专项应付款",
    "estimated_liab": "预计负债",
    "defer_tax_liab": "递延所得税负债",
    "defer_inc_non_cur_liab": "递延收益-非流动负债",
    "oth_ncl": "其他非流动负债",
    "total_ncl": "非流动负债合计",
    "depos_oth_bfi": "同业和其它金融机构存放款项",
    "deriv_liab": "衍生金融负债",
    "depos": "吸收存款",
    "agency_bus_liab": "代理业务负债",
    "oth_liab": "其他负债",
    "prem_receiv_adva": "预收保费",
    "depos_received": "存入保证金",
    "ph_invest": "保户储金及投资款",
    "reser_une_prem": "未到期责任准备金",
    "reser_outstd_claims": "未决赔款准备金",
    "reser_lins_liab": "寿险责任准备金",
    "reser_lthins_liab": "长期健康险责任准备金",
    "indept_acc_liab": "独立账户负债",
    "pledge_borr": "其中:质押借款",
    "indem_payable": "应付赔付款",
    "policy_div_payable": "应付保单红利",
    "total_liab": "负债合计",
    "treasury_share": "减:库存股",
    "ordin_risk_reser": "一般风险准备",
    "forex_differ": "外币报表折算差额",
    "invest_loss_unconf": "未确认的投资损失",
    "minority_int": "少数股东权益",
    "total_hldr_eqy_exc_min_int": "股东权益合计(不含少数股东权益)",
    "total_hldr_eqy_inc_min_int": "股东权益合计(含少数股东权益)",
    "total_liab_hldr_eqy": "负债及股东权益总计",
    "lt_payroll_payable": "长期应付职工薪酬",
    "oth_comp_income": "其他综合收益",
    "oth_eqt_tools": "其他权益工具",
    "oth_eqt_tools_p_shr": "其他权益工具(优先股)",
    "lending_funds": "融出资金",
    "acc_receivable": "应收款项",
    "st_fin_payable": "应付短期融资款",
    "payables": "应付款项",
    "hfs_assets": "持有待售的资产",
    "hfs_sales": "持有待售的负债",
    "cost_fin_assets": "以摊余成本计量的金融资产",
    "fair_value_fin_assets": "以公允价值计量且其变动计入其他综合收益的金融资产",
    "receiv_financing": "应收款项融资",
    "use_right_assets": "使用权资产",
    "lease_liab": "租赁负债",
    "contract_assets": "合同资产",
    "contract_liab": "合同负债",
    "accounts_receiv_bill": "应收票据及应收账款",
    "accounts_pay": "应付票据及应付账款",
    "update_flag": "更新标识"
}

variables_dict_3 = {
    "ts_code": "TS股票代码",
    "ann_date": "公告日期",
    "f_ann_date": "实际公告日期",
    "end_date": "报告期",
    "comp_type": "公司类型(1一般工商业2银行3保险4证券)",
    "report_type": "报表类型",
    "end_type": "报告期类型",
    "net_profit": "净利润",
    "finan_exp": "财务费用",
    "c_fr_sale_sg": "销售商品、提供劳务收到的现金",
    "recp_tax_rends": "收到的税费返还",
    "n_depos_incr_fi": "客户存款和同业存放款项净增加额",
    "n_incr_loans_cb": "向中央银行借款净增加额",
    "n_inc_borr_oth_fi": "向其他金融机构拆入资金净增加额",
    "prem_fr_orig_contr": "收到原保险合同保费取得的现金",
    "n_incr_insured_dep": "保户储金净增加额",
    "n_reinsur_prem": "收到再保业务现金净额",
    "n_incr_disp_tfa": "处置交易性金融资产净增加额",
    "ifc_cash_incr": "收取利息和手续费净增加额",
    "n_incr_disp_faas": "处置可供出售金融资产净增加额",
    "n_incr_loans_oth_bank": "拆入资金净增加额",
    "n_cap_incr_repur": "回购业务资金净增加额",
    "c_fr_oth_operate_a": "收到其他与经营活动有关的现金",
    "c_inf_fr_operate_a": "经营活动现金流入小计",
    "c_paid_goods_s": "购买商品、接受劳务支付的现金",
    "c_paid_to_for_empl": "支付给职工以及为职工支付的现金",
    "c_paid_for_taxes": "支付的各项税费",
    "n_incr_clt_loan_adv": "客户贷款及垫款净增加额",
    "n_incr_dep_cbob": "存放央行和同业款项净增加额",
    "c_pay_claims_orig_inco": "支付原保险合同赔付款项的现金",
    "pay_handling_chrg": "支付手续费的现金",
    "pay_comm_insur_plcy": "支付保单红利的现金",
    "oth_cash_pay_oper_act": "支付其他与经营活动有关的现金",
    "st_cash_out_act": "经营活动现金流出小计",
    "n_cashflow_act": "经营活动产生的现金流量净额",
    "oth_recp_ral_inv_act": "收到其他与投资活动有关的现金",
    "c_disp_withdrwl_invest": "收回投资收到的现金",
    "c_recp_return_invest": "取得投资收益收到的现金",
    "n_recp_disp_fiolta": "处置固定资产、无形资产和其他长期资产收回的现金净额",
    "n_recp_disp_sobu": "处置子公司及其他营业单位收到的现金净额",
    "stot_inflows_inv_act": "投资活动现金流入小计",
    "c_pay_acq_const_fiolta": "购建固定资产、无形资产和其他长期资产支付的现金",
    "c_paid_invest": "投资支付的现金",
    "n_disp_subs_oth_biz": "取得子公司及其他营业单位支付的现金净额",
    "oth_pay_ral_inv_act": "支付其他与投资活动有关的现金",
    "n_incr_pledge_loan": "质押贷款净增加额",
    "stot_out_inv_act": "投资活动现金流出小计",
    "n_cashflow_inv_act": "投资活动产生的现金流量净额",
    "c_recp_borrow": "取得借款收到的现金",
    "proc_issue_bonds": "发行债券收到的现金",
    "oth_cash_recp_ral_fnc_act": "收到其他与筹资活动有关的现金",
    "stot_cash_in_fnc_act": "筹资活动现金流入小计",
    "free_cashflow": "企业自由现金流量",
    "c_prepay_amt_borr": "偿还债务支付的现金",
    "c_pay_dist_dpcp_int_exp": "分配股利、利润或偿付利息支付的现金",
    "incl_dvd_profit_paid_sc_ms": "其中:子公司支付给少数股东的股利、利润",
    "oth_cashpay_ral_fnc_act": "支付其他与筹资活动有关的现金",
    "stot_cashout_fnc_act": "筹资活动现金流出小计",
    "n_cash_flows_fnc_act": "筹资活动产生的现金流量净额",
    "eff_fx_flu_cash": "汇率变动对现金的影响",
    "n_incr_cash_cash_equ": "现金及现金等价物净增加额",
    "c_cash_equ_beg_period": "期初现金及现金等价物余额",
    "c_cash_equ_end_period": "期末现金及现金等价物余额",
    "c_recp_cap_contrib": "吸收投资收到的现金",
    "incl_cash_rec_saims": "其中:子公司吸收少数股东投资收到的现金",
    "uncon_invest_loss": "未确认投资损失",
    "prov_depr_assets": "加:资产减值准备",
    "depr_fa_coga_dpba": "固定资产折旧、油气资产折耗、生产性生物资产折旧",
    "amort_intang_assets": "无形资产摊销",
    "lt_amort_deferred_exp": "长期待摊费用摊销",
    "decr_deferred_exp": "待摊费用减少",
    "incr_acc_exp": "预提费用增加",
    "loss_disp_fiolta": "处置固定、无形资产和其他长期资产的损失",
    "loss_scr_fa": "固定资产报废损失",
    "loss_fv_chg": "公允价值变动损失",
    "invest_loss": "投资损失",
    "decr_def_inc_tax_assets": "递延所得税资产减少",
    "incr_def_inc_tax_liab": "递延所得税负债增加",
    "decr_inventories": "存货的减少",
    "decr_oper_payable": "经营性应收项目的减少",
    "incr_oper_payable": "经营性应付项目的增加",
    "others": "其他",
    "im_net_cashflow_oper_act": "经营活动产生的现金流量净额(间接法)",
    "conv_debt_into_cap": "债务转为资本",
    "conv_copbonds_due_within_1y": "一年内到期的可转换公司债券",
    "fa_fnc_leases": "融资租入固定资产",
    "im_n_incr_cash_equ": "现金及现金等价物净增加额(间接法)",
    "net_dism_capital_add": "拆出资金净增加额",
    "net_cash_rece_sec": "代理买卖证券收到的现金净额(元)",
    "credit_impa_loss": "信用减值损失",
    "use_right_asset_dep": "使用权资产折旧",
    "oth_loss_asset": "其他资产减值损失",
    "end_bal_cash": "现金的期末余额",
    "beg_bal_cash": "减:现金的期初余额",
    "end_bal_cash_equ": "加:现金等价物的期末余额",
    "beg_bal_cash_equ": "减:现金等价物的期初余额",
    "update_flag": "更新标志(1最新）"
}

def get_annual_data(df):
    """筛选年度数据（12月31日）"""
    return df[df['报告期'].str.contains('1231')].reset_index(drop=True)

@st.cache_data
def get_stock_info():
    """获取股票基础信息"""
    try:
        # 添加延时避免频繁调用
        time.sleep(0.5)
        data = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name,area,industry,list_date')
        return data
    except Exception as e:
        st.error(f"获取股票信息失败: {e}")
        return None

def check_api_quota(api_key):
    """检查API配额"""
    try:
        user_info = pro.user(token=api_key)
        points = user_info.iloc[0]['points']
        return points
    except Exception as e:
        st.error(f"检查API配额失败: {e}")
        return 0

def safe_api_call(func, *args, **kwargs):
    """安全的API调用，包含重试机制"""
    max_retries = 3
    retry_delay = 2

    for attempt in range(max_retries):
        try:
            # 添加延时避免频繁调用
            time.sleep(1)
            result = func(*args, **kwargs)
            return result
        except Exception as e:
            error_msg = str(e)
            if "每天最多访问" in error_msg or "权限" in error_msg:
                st.error(f"❌ API配额不足: {error_msg}")
                st.info("💡 建议解决方案：")
                st.info("1. 升级到更高级别的Tushare账户")
                st.info("2. 减少查询的股票数量")
                st.info("3. 明天再试（配额会重置）")
                st.info("4. 访问 https://tushare.pro/document/1?doc_id=108 查看权限详情")
                return None
            elif attempt < max_retries - 1:
                st.warning(f"⚠️ API调用失败，{retry_delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                time.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                st.error(f"❌ API调用最终失败: {error_msg}")
                return None
    return None

def Company_Financial_Data(code='', start_date='20160101', end_date='20231231', kind='季度'):
    """获取公司财务数据"""
    try:
        # 获取利润表数据
        income_columns_drop = ['公告日期', '实际公告日期', '报告类型', '公司类型(1一般工商业2银行3保险4证券)', '报告期类型']
        income_data = safe_api_call(pro.income, ts_code=code, start_date=start_date, end_date=end_date)
        if income_data is None or income_data.empty:
            st.warning(f"股票 {code} 没有利润表数据")
            return pd.DataFrame()

        income_data = income_data.rename(columns=variables_dict).iloc[:, :-1].drop_duplicates(['报告期'])
        income_data = income_data.drop(columns=[col for col in income_columns_drop if col in income_data.columns])

        # 获取资产负债表数据
        balance_columns_drop = ['公告日期', '实际公告日期', '报表类型', '公司类型(1一般工商业2银行3保险4证券)', '报告期类型']
        balance_sheet = safe_api_call(pro.balancesheet, ts_code=code, start_date=start_date, end_date=end_date)
        if balance_sheet is None or balance_sheet.empty:
            st.warning(f"股票 {code} 没有资产负债表数据")
            return pd.DataFrame()

        balance_sheet = balance_sheet.rename(columns=variables_dict_2).iloc[:, :-1].drop_duplicates(['报告期'])
        balance_sheet = balance_sheet.drop(columns=[col for col in balance_columns_drop if col in balance_sheet.columns])

        # 获取现金流量表数据
        cash_columns_drop = ['公告日期', '实际公告日期', '报表类型', '公司类型(1一般工商业2银行3保险4证券)', '报告期类型']
        cash_flow = safe_api_call(pro.cashflow, ts_code=code, start_date=start_date, end_date=end_date)
        if cash_flow is None or cash_flow.empty:
            st.warning(f"股票 {code} 没有现金流量表数据")
            return pd.DataFrame()

        cash_flow = cash_flow.rename(columns=variables_dict_3).iloc[:, :-1].drop_duplicates(['报告期'])
        cash_flow = cash_flow.drop(columns=[col for col in cash_columns_drop if col in cash_flow.columns])

        # 如果选择年度数据，只保留12月31日的数据
        if kind == '年度':
            balance_sheet = get_annual_data(balance_sheet)
            income_data = get_annual_data(income_data)
            cash_flow = get_annual_data(cash_flow)

        # 合并三张表
        merged_df = pd.merge(cash_flow, balance_sheet, on='报告期', how='inner', suffixes=('', '_balance'))
        final_merged_df = pd.merge(merged_df, income_data, on='报告期', how='inner', suffixes=('', '_income'))

        # 删除重复的股票代码列
        cols_to_drop = ['TS股票代码_balance', 'TS股票代码_income']
        final_merged_df = final_merged_df.drop(columns=[col for col in cols_to_drop if col in final_merged_df.columns])

        # 删除全为NaN的列
        final_merged_df = final_merged_df.dropna(axis=1, how='all')

        return final_merged_df
    except Exception as e:
        st.error(f"获取 {code} 财务数据失败: {e}")
        return pd.DataFrame()

def Financial_Data_analysis(financial_datas):
    """财务数据分析，计算各种财务指标"""
    if financial_datas.empty:
        return pd.DataFrame()
        
    financial_data = financial_datas.copy()
    
    try:
        # 转换报告期为日期格式
        financial_data['报告期'] = pd.to_datetime(financial_data['报告期'], format='%Y%m%d')
        financial_data['年份'] = financial_data['报告期'].dt.year
    except Exception as e:
        st.warning(f"日期转换失败: {e}")
        return pd.DataFrame()

    # 计算财务指标
    try:
        # 净资产收益率 (ROE)
        if '净利润' in financial_data.columns and '股东权益合计(含少数股东权益)' in financial_data.columns:
            financial_data['净资产收益率'] = financial_data['净利润'] / financial_data['股东权益合计(含少数股东权益)']
        elif '净利润(含少数股东损益)' in financial_data.columns and '股东权益合计(含少数股东权益)' in financial_data.columns:
            financial_data['净资产收益率'] = financial_data['净利润(含少数股东损益)'] / financial_data['股东权益合计(含少数股东权益)']
    except:
        pass

    try:
        # 总资产报酬率 (ROA)
        if '净利润' in financial_data.columns and '资产总计' in financial_data.columns:
            financial_data['总资产报酬率'] = financial_data['净利润'] / financial_data['资产总计']
        elif '净利润(含少数股东损益)' in financial_data.columns and '资产总计' in financial_data.columns:
            financial_data['总资产报酬率'] = financial_data['净利润(含少数股东损益)'] / financial_data['资产总计']
    except:
        pass

    try:
        # 应收账款周转率
        if '营业收入' in financial_data.columns and '应收账款' in financial_data.columns:
            financial_data['应收账款周转率'] = financial_data['营业收入'] / financial_data['应收账款']
    except:
        pass

    try:
        # 资产负债率
        if '负债合计' in financial_data.columns and '资产总计' in financial_data.columns:
            financial_data['资产负债率'] = financial_data['负债合计'] / financial_data['资产总计']
    except:
        pass

    try:
        # 速动比率
        if '货币资金' in financial_data.columns and '应收账款' in financial_data.columns and '流动负债合计' in financial_data.columns:
            financial_data['速动比率'] = (financial_data['货币资金'] + financial_data['应收账款']) / financial_data['流动负债合计']
    except:
        pass

    try:
        # 存货周转率
        if '营业总成本' in financial_data.columns and '存货' in financial_data.columns:
            financial_data['存货周转率'] = financial_data['营业总成本'] / financial_data['存货']
    except:
        pass

    try:
        # 流动比率
        if '流动资产合计' in financial_data.columns and '流动负债合计' in financial_data.columns:
            financial_data['流动比率'] = financial_data['流动资产合计'] / financial_data['流动负债合计']
    except:
        pass

    try:
        # 销售毛利率
        if '营业总收入' in financial_data.columns and '营业总成本' in financial_data.columns:
            financial_data['销售毛利率'] = (financial_data['营业总收入'] - financial_data['营业总成本']) / financial_data['营业总收入']
    except:
        pass

    try:
        # 计算增长率（需要按股票代码和年份排序）
        financial_data_sorted = financial_data.sort_values(by=['TS股票代码', '年份'])
        
        # 销售增长率
        if '营业收入' in financial_data_sorted.columns:
            financial_data_sorted['销售增长率'] = financial_data_sorted.groupby('TS股票代码')['营业收入'].pct_change()
        
        # 销售利润增长率
        if '营业利润' in financial_data_sorted.columns:
            financial_data_sorted['销售利润增长率'] = financial_data_sorted.groupby('TS股票代码')['营业利润'].pct_change()
    except:
        financial_data_sorted = financial_data.copy()

    # 选择要显示的列
    available_columns = ['TS股票代码', '年份', '净资产收益率', '总资产报酬率', '应收账款周转率', 
                        '资产负债率', '速动比率', '销售增长率', '销售利润增长率', '存货周转率', 
                        '销售毛利率', '流动比率']
    
    columns_to_display = [col for col in available_columns if col in financial_data_sorted.columns]
    
    if columns_to_display:
        financial_ratios = financial_data_sorted[columns_to_display]
        return financial_ratios
    else:
        return pd.DataFrame()

def main():
    """主函数"""
    st.set_page_config(page_title="财务数据获取与分析系统", page_icon="📊", layout="wide")
    
    st.title("📊 财务数据获取与分析系统")
    st.markdown("---")
    
    # 侧边栏配置
    with st.sidebar:
        st.header("🔧 配置")
        
        # API密钥输入
        api_key = st.text_input("请输入Tushare API密钥:", type="password", 
                               help="请到 https://tushare.pro 注册并获取API密钥")
        
        if api_key:
            try:
                ts.set_token(api_key)
                global pro
                pro = ts.pro_api()

                # 验证API密钥并检查配额
                points = check_api_quota(api_key)
                if points > 0:
                    st.success("✅ API密钥验证成功!")

                    # 根据积分显示不同的提示
                    if points >= 2000:
                        st.success(f"🎉 积分充足: {points} (推荐)")
                    elif points >= 500:
                        st.warning(f"⚠️ 积分较少: {points} (建议减少查询数量)")
                        st.info("💡 提示：可以选择较少的股票或使用年度数据减少API调用")
                    else:
                        st.error(f"❌ 积分不足: {points} (可能无法正常使用)")
                        st.info("💡 建议：升级账户或明天再试")

                    # 显示使用建议
                    with st.expander("📋 使用建议"):
                        st.write("**根据您的积分情况：**")
                        if points >= 2000:
                            st.write("- ✅ 可以正常查询多只股票")
                            st.write("- ✅ 支持季度和年度数据")
                        elif points >= 500:
                            st.write("- ⚠️ 建议一次查询不超过5只股票")
                            st.write("- ⚠️ 优先选择年度数据（调用次数更少）")
                        else:
                            st.write("- ❌ 建议升级账户或明天再试")
                            st.write("- 💡 访问 https://tushare.pro 升级账户")
                else:
                    st.error("❌ 无法获取账户信息，请检查API密钥")
                    st.stop()

            except Exception as e:
                error_msg = str(e)
                if "每天最多访问" in error_msg or "权限" in error_msg:
                    st.error(f"❌ API配额限制: {error_msg}")
                    st.info("💡 解决方案：")
                    st.info("1. 升级到更高级别的Tushare账户")
                    st.info("2. 明天再试（配额会重置）")
                    st.info("3. 访问 https://tushare.pro/document/1?doc_id=108 查看权限详情")
                else:
                    st.error(f"❌ API密钥验证失败: {error_msg}")
                st.stop()
        else:
            st.warning("⚠️ 请先输入API密钥")
            st.stop()

    # 创建三个标签页
    tab1, tab2, tab3 = st.tabs(["📥 财务数据下载", "📈 财务数据分析", "ℹ️ 使用说明"])

    with tab1:
        st.header("📥 财务数据下载")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 股票选择方式
            input_method = st.radio("选择输入方式:", ["手动输入股票名称", "从股票列表选择"])
            
            if input_method == "手动输入股票名称":
                stock_input = st.text_input('请输入股票名称（英文逗号分隔）:', 
                                          value='贵州茅台,格力电器',
                                          help="例如: 贵州茅台,格力电器,比亚迪")
                codes_names = [code.strip() for code in stock_input.split(',') if code.strip()]
            else:
                stock_data = get_stock_info()
                if stock_data is not None:
                    selected_stocks = st.multiselect(
                        "选择股票:",
                        options=stock_data['name'].tolist(),
                        default=['贵州茅台', '格力电器'],
                        help="可以输入股票名称进行搜索"
                    )
                    codes_names = selected_stocks
                else:
                    st.error("无法获取股票列表")
                    codes_names = []
        
        with col2:
            # 日期选择
            current_year = datetime.now().year
            start_year = st.selectbox("开始年份:", 
                                    options=list(range(2000, current_year + 1)),
                                    index=list(range(2000, current_year + 1)).index(current_year - 3))
            end_year = st.selectbox("结束年份:", 
                                  options=list(range(2000, current_year + 1)),
                                  index=list(range(2000, current_year + 1)).index(current_year - 1))
            
            # 数据类型选择
            kind = st.selectbox("选择财务数据类型:", ["季度", "年度"])
        
        if codes_names:
            st.write("选择的股票：", ", ".join(codes_names))
            
            # 转换日期格式
            start_date = f'{start_year}0101'
            end_date = f'{end_year}1231'
            
            # 下载按钮
            if st.button('🚀 开始获取财务数据', type="primary"):
                # 检查股票数量和积分
                points = check_api_quota(api_key)
                estimated_calls = len(codes_names) * 3  # 每只股票需要3次API调用（利润表、资产负债表、现金流量表）

                if points < 500 and len(codes_names) > 2:
                    st.error(f"❌ 积分不足({points})，建议查询股票数量不超过2只")
                    st.stop()
                elif points < 2000 and len(codes_names) > 5:
                    st.warning(f"⚠️ 积分较少({points})，建议查询股票数量不超过5只")
                    if not st.checkbox("我了解风险，继续执行"):
                        st.stop()

                st.info(f"📊 预计需要 {estimated_calls} 次API调用，当前积分: {points}")

                try:
                    stock_data = get_stock_info()
                    if stock_data is None:
                        st.error("无法获取股票基础信息")
                        st.stop()

                    codes = []
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    # 获取股票代码
                    for i, stock_name in enumerate(codes_names):
                        status_text.text(f'正在查询 {stock_name} 的股票代码...')
                        try:
                            stock_code = stock_data[stock_data['name'].str.contains(stock_name, na=False)]['ts_code']
                            if not stock_code.empty:
                                code = stock_code.iloc[0]
                                codes.append(code)
                                st.success(f'✅ {stock_name} 的股票代码: {code}')
                            else:
                                st.warning(f'⚠️ 无法找到 {stock_name} 对应的股票代码')
                        except Exception as e:
                            st.error(f'❌ 查询 {stock_name} 失败: {e}')

                        progress_bar.progress((i + 1) / len(codes_names) * 0.3)
                    
                    if codes:
                        st.info(f"开始获取 {len(codes)} 只股票从 {start_date} 到 {end_date} 的{kind}财务数据")
                        st.info(f"⏱️ 预计耗时: {len(codes) * 10} 秒（包含API调用延时）")

                        # 获取财务数据
                        with pd.ExcelWriter('上市公司财务数据.xlsx', engine='openpyxl') as writer:
                            successful_downloads = 0
                            failed_downloads = 0

                            for i, code in enumerate(codes):
                                status_text.text(f'正在获取 {code} 的财务数据... ({i+1}/{len(codes)})')
                                try:
                                    df = Company_Financial_Data(code=code,
                                                              start_date=start_date,
                                                              end_date=end_date,
                                                              kind=kind)
                                    if not df.empty:
                                        # 使用股票名称作为sheet名称
                                        stock_name = codes_names[codes_names.index(stock_data[stock_data['ts_code'] == code]['name'].iloc[0]) if code in stock_data['ts_code'].values else i]
                                        sheet_name = code.replace('.', '_')[:31]  # Excel sheet名称限制
                                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                                        st.success(f'✅ {code} 财务数据获取成功')
                                        successful_downloads += 1
                                    else:
                                        st.warning(f'⚠️ {code} 没有可用的财务数据')
                                        failed_downloads += 1
                                except Exception as e:
                                    error_msg = str(e)
                                    if "每天最多访问" in error_msg or "权限" in error_msg:
                                        st.error(f'❌ {code} API配额不足，停止后续查询')
                                        break
                                    else:
                                        st.error(f'❌ {code} 财务数据获取失败: {e}')
                                        failed_downloads += 1

                                progress_bar.progress(0.3 + (i + 1) / len(codes) * 0.7)

                            status_text.text('数据获取完成!')

                        # 显示结果统计
                        st.markdown("### 📊 获取结果统计")
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("成功", successful_downloads, delta=None)
                        with col2:
                            st.metric("失败", failed_downloads, delta=None)
                        with col3:
                            st.metric("总计", len(codes), delta=None)

                        if successful_downloads > 0:
                            st.success(f'🎉 成功获取 {successful_downloads} 只股票的财务数据！')
                            st.info("📁 数据已保存为：上市公司财务数据.xlsx")

                            # 提供下载链接
                            with open('上市公司财务数据.xlsx', 'rb') as file:
                                st.download_button(
                                    label="📥 下载财务数据文件",
                                    data=file,
                                    file_name="上市公司财务数据.xlsx",
                                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                                )
                        else:
                            st.error("❌ 没有成功获取任何财务数据")
                    else:
                        st.error("❌ 没有找到有效的股票代码")
                        
                except Exception as e:
                    st.error(f"获取数据时发生错误: {e}")

    with tab2:
        st.header("📈 财务数据分析")
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "上传财务数据Excel文件:",
            type=['xlsx', 'xls'],
            help="请上传包含财务数据的Excel文件"
        )
        
        if uploaded_file is not None:
            try:
                # 读取Excel文件的所有sheet
                xls = pd.ExcelFile(uploaded_file)
                sheet_names = xls.sheet_names
                
                st.success(f"✅ 文件上传成功！发现 {len(sheet_names)} 个数据表")
                st.write("数据表列表:", sheet_names)
                
                # 选择要分析的sheet
                selected_sheets = st.multiselect(
                    "选择要分析的数据表:",
                    options=sheet_names,
                    default=sheet_names,
                    help="可以选择多个数据表进行分析"
                )
                
                if selected_sheets and st.button('📊 开始财务指标计算', type="primary"):
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    # 创建结果Excel文件
                    with pd.ExcelWriter('财务数据计算的指标.xlsx', engine='openpyxl') as writer:
                        successful_analysis = 0
                        
                        for i, sheet_name in enumerate(selected_sheets):
                            status_text.text(f'正在分析 {sheet_name} 的财务数据...')
                            
                            try:
                                # 读取原始财务数据
                                financial_datas = pd.read_excel(uploaded_file, sheet_name=sheet_name)
                                
                                # 进行财务分析
                                analysis_result = Financial_Data_analysis(financial_datas)
                                
                                if not analysis_result.empty:
                                    # 保存分析结果
                                    analysis_result.to_excel(writer, sheet_name=sheet_name, index=False)
                                    st.success(f'✅ {sheet_name} 财务指标计算完成')
                                    successful_analysis += 1
                                    
                                    # 显示部分分析结果
                                    with st.expander(f"📋 {sheet_name} 分析结果预览"):
                                        st.dataframe(analysis_result.head())
                                        
                                        # 显示基本统计信息
                                        if len(analysis_result) > 1:
                                            st.subheader("📊 财务指标统计")
                                            numeric_cols = analysis_result.select_dtypes(include=['float64', 'int64']).columns
                                            if len(numeric_cols) > 0:
                                                st.dataframe(analysis_result[numeric_cols].describe())
                                else:
                                    st.warning(f'⚠️ {sheet_name} 无法计算财务指标（数据不足或格式问题）')
                                    
                            except Exception as e:
                                st.error(f'❌ {sheet_name} 分析失败: {e}')
                            
                            progress_bar.progress((i + 1) / len(selected_sheets))
                        
                        status_text.text('财务指标计算完成!')
                    
                    if successful_analysis > 0:
                        st.success(f'🎉 成功分析 {successful_analysis} 个数据表！')
                        st.info("📁 分析结果已保存为：财务数据计算的指标.xlsx")
                        
                        # 提供下载链接
                        with open('财务数据计算的指标.xlsx', 'rb') as file:
                            st.download_button(
                                label="📥 下载财务指标分析结果",
                                data=file,
                                file_name="财务数据计算的指标.xlsx",
                                mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            )
                    else:
                        st.error("❌ 没有成功分析任何数据表")
                        
            except Exception as e:
                st.error(f"处理文件时发生错误: {e}")
        else:
            st.info("📤 请上传Excel文件开始分析")
            
            # 显示示例数据格式
            st.subheader("📋 支持的财务指标")
            indicators_df = pd.DataFrame({
                '指标名称': ['净资产收益率', '总资产报酬率', '应收账款周转率', '资产负债率', 
                          '速动比率', '流动比率', '销售毛利率', '存货周转率', '销售增长率', '销售利润增长率'],
                '计算公式': ['净利润 / 股东权益', '净利润 / 资产总计', '营业收入 / 应收账款', '负债合计 / 资产总计',
                          '(货币资金 + 应收账款) / 流动负债合计', '流动资产合计 / 流动负债合计', 
                          '(营业总收入 - 营业总成本) / 营业总收入', '营业总成本 / 存货', 
                          '营业收入同比增长', '营业利润同比增长'],
                '说明': ['衡量股东权益的盈利能力', '衡量资产使用效率', '衡量应收账款管理效率', '衡量财务风险',
                       '衡量短期偿债能力', '衡量流动性', '衡量盈利能力', '衡量存货管理效率', 
                       '衡量收入增长情况', '衡量利润增长情况']
            })
            st.dataframe(indicators_df, use_container_width=True)

    with tab3:
        st.header("ℹ️ 使用说明")

        st.markdown("""
        ### 🔑 API密钥获取
        1. 访问 [Tushare官网](https://tushare.pro) 注册账户
        2. 在个人中心获取API密钥
        3. 将API密钥输入到左侧配置栏

        ### 📊 积分说明
        - **2000+积分**: 可以正常使用所有功能
        - **500-2000积分**: 建议减少查询股票数量（≤5只）
        - **<500积分**: 建议升级账户或明天再试

        ### ⚠️ 常见问题
        **Q: 提示"每天最多访问50次"怎么办？**
        A: 这是免费账户的限制，解决方案：
        - 升级到付费账户
        - 减少查询的股票数量
        - 选择年度数据（API调用次数更少）
        - 明天再试（配额会重置）

        **Q: 如何减少API调用次数？**
        A:
        - 选择"年度"数据类型而不是"季度"
        - 一次查询较少的股票（建议≤5只）
        - 缩短查询的时间范围

        **Q: 数据获取失败怎么办？**
        A:
        - 检查网络连接
        - 确认股票名称正确
        - 检查API密钥是否有效
        - 查看是否超出配额限制

        ### 💡 使用建议
        1. **首次使用**: 建议先查询1-2只股票测试
        2. **批量查询**: 根据积分情况合理安排查询数量
        3. **数据类型**: 如果只需要年度数据，选择"年度"可以节省配额
        4. **时间范围**: 根据需要选择合适的时间范围，避免不必要的数据

        ### 📞 技术支持
        - Tushare官方文档: https://tushare.pro/document/2
        - 权限说明: https://tushare.pro/document/1?doc_id=108
        """)

if __name__ == "__main__":
    main()