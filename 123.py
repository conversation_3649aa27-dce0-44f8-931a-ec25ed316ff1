import tushare as ts
import pandas as pd
from datetime import datetime, timedelta

# 设置tushare token（需要替换为你的token）
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def get_gree_ttm_data():
    """
    获取格力电器(000651.SZ)的TTM财务数据
    """
    # 格力电器股票代码
    stock_code = '000651.SZ'
    
    try:
        # 获取最近的财务数据
        # 利润表数据 - 获取净利润
        income_df = pro.income(ts_code=stock_code, 
                              fields='ts_code,end_date,n_income,n_income_attr_p')
        
        # 现金流量表数据 - 获取经营活动现金流净额
        cashflow_df = pro.cashflow(ts_code=stock_code,
                                  fields='ts_code,end_date,n_cashflow_act')
        
        # 去重处理（避免相同日期重复）
        if not cashflow_df.empty:
            cashflow_df = cashflow_df.drop_duplicates(subset=['end_date'])
        
        print("=== 格力电器(000651.SZ) TTM财务数据 ===\n")
        
        # 处理净利润数据
        if not income_df.empty:
            # 按日期排序，取最近4个季度数据计算TTM
            income_df['end_date'] = pd.to_datetime(income_df['end_date'])
            income_df = income_df.sort_values('end_date', ascending=False)
            
            print("近期净利润数据（按季度）:")
            print("-" * 50)
            for i in range(min(4, len(income_df))):
                row = income_df.iloc[i]
                print(f"{row['end_date'].strftime('%Y-%m-%d')}: "
                      f"净利润 {row['n_income']:.2f}万元, "
                      f"归母净利润 {row['n_income_attr_p']:.2f}万元")
            
            # 计算TTM净利润（最近4个季度）
            if len(income_df) >= 4:
                ttm_net_income = income_df.head(4)['n_income'].sum()
                ttm_net_income_attr = income_df.head(4)['n_income_attr_p'].sum()
                
                print(f"\n📊 净利润TTM（最近12个月）:")
                print(f"   净利润TTM: {ttm_net_income:.2f}万元 ({ttm_net_income/10000:.2f}亿元)")
                print(f"   归母净利润TTM: {ttm_net_income_attr:.2f}万元 ({ttm_net_income_attr/10000:.2f}亿元)")
            else:
                print(f"⚠️ 数据不足，仅有{len(income_df)}个季度数据")
        
        print("\n" + "="*60 + "\n")
        
        # 处理现金流数据
        if not cashflow_df.empty:
            cashflow_df['end_date'] = pd.to_datetime(cashflow_df['end_date'])
            cashflow_df = cashflow_df.sort_values('end_date', ascending=False)
            
            print("近期现金流数据（按季度）:")
            print("-" * 50)
            for i in range(min(4, len(cashflow_df))):
                row = cashflow_df.iloc[i]
                print(f"{row['end_date'].strftime('%Y-%m-%d')}: "
                      f"经营活动现金流净额 {row['n_cashflow_act']:.2f}万元")
            
            # 计算TTM现金流（最近4个季度）
            if len(cashflow_df) >= 4:
                # 修正单位问题：检查数据规模来判断单位
                recent_4_quarters = cashflow_df.head(4)
                print("\n原始数据检查:")
                for idx, row in recent_4_quarters.iterrows():
                    raw_value = row['n_cashflow_act']
                    print(f"  {row['end_date'].strftime('%Y-%m-%d')}: 原始值 {raw_value}")
                
                # 根据数据规模判断是否需要单位转换
                first_value = recent_4_quarters.iloc[0]['n_cashflow_act']
                if abs(first_value) > 1000000000:  # 如果大于10亿，可能已经是元为单位
                    print("检测到数据可能已经是元为单位，直接使用")
                    ttm_cashflow = recent_4_quarters['n_cashflow_act'].sum()
                else:
                    print("检测到数据是万元单位，转换为元")
                    ttm_cashflow = recent_4_quarters['n_cashflow_act'].sum() * 10000
                
                print(f"\n💰 现金流TTM（最近12个月）:")
                print(f"   经营活动现金流净额TTM: {ttm_cashflow:.2f}元 ({ttm_cashflow/100000000:.2f}亿元)")
            else:
                print(f"⚠️ 数据不足，仅有{len(cashflow_df)}个季度数据")
        
        print("\n" + "="*60)
        
        # 返回TTM数据字典
        result = {}
        if len(income_df) >= 4:
            result['净利润TTM(万元)'] = income_df.head(4)['n_income'].sum()
            result['归母净利润TTM(万元)'] = income_df.head(4)['n_income_attr_p'].sum()
        
        if len(cashflow_df) >= 4:
            result['现金流TTM(万元)'] = cashflow_df.head(4)['n_cashflow_act'].sum()
        
        return result
        
    except Exception as e:
        print(f"❌ 获取数据失败: {str(e)}")
        print("请检查:")
        print("1. tushare token是否正确设置")
        print("2. 网络连接是否正常")
        print("3. tushare积分是否足够")
        return None

def get_latest_financial_indicators():
    """
    获取格力电器最新的财务指标数据（包含TTM数据）
    """
    stock_code = '000651.SZ'
    
    try:
        # 获取财务指标数据
        fina_indicator = pro.fina_indicator(ts_code=stock_code)
        
        if not fina_indicator.empty:
            # 按日期排序，取最新数据
            fina_indicator['end_date'] = pd.to_datetime(fina_indicator['end_date'])
            latest_data = fina_indicator.sort_values('end_date', ascending=False).iloc[0]
            
            print("\n📈 最新财务指标（可能包含TTM数据）:")
            print("-" * 50)
            print(f"报告期: {latest_data['end_date'].strftime('%Y-%m-%d')}")
            
            # 查看相关TTM指标
            ttm_fields = ['netprofit_ttm', 'revenue_ttm', 'ebit_ttm', 'ebitda_ttm']
            for field in ttm_fields:
                if field in latest_data and pd.notna(latest_data[field]):
                    print(f"{field}: {latest_data[field]:.2f}万元")
            
            return latest_data
        
    except Exception as e:
        print(f"❌ 获取财务指标失败: {str(e)}")
        return None

def verify_specific_ttm_data(target_date='20250331', expected_cashflow_ttm=30886177526.69):
    """
    验证特定日期的TTM数据
    """
    stock_code = '000651.SZ'
    
    print(f"🔍 验证 {target_date} 的TTM数据:")
    print(f"待验证现金流TTM: {expected_cashflow_ttm:.2f}元 ({expected_cashflow_ttm/100000000:.2f}亿元)")
    print("-" * 60)
    
    try:
        # 获取现金流数据（不指定具体日期，让tushare返回所有数据）
        cashflow_df = pro.cashflow(ts_code=stock_code,
                                  fields='ts_code,end_date,n_cashflow_act')
        
        if cashflow_df.empty:
            print("❌ 无法获取现金流数据")
            return None
            
        # 数据预处理
        cashflow_df['end_date'] = pd.to_datetime(cashflow_df['end_date'])
        cashflow_df = cashflow_df.drop_duplicates(subset=['end_date'])
        cashflow_df = cashflow_df.sort_values('end_date', ascending=False)
        
        # 找到目标日期对应的数据点
        target_dt = pd.to_datetime(target_date)
        
        # 获取目标日期及之前最近的4个季度数据
        recent_data = cashflow_df[cashflow_df['end_date'] <= target_dt].head(4)
        
        print("用于计算TTM的季度数据:")
        total_cashflow = 0
        
        for idx, row in recent_data.iterrows():
            # 修正单位转换：tushare现金流数据似乎已经是元为单位，不需要乘以10000
            # 先尝试不同的单位转换方式
            raw_value = float(row['n_cashflow_act'])
            
            # 判断数据单位：如果数值过大，可能已经是元为单位
            if abs(raw_value) > 1000000000:  # 如果大于10亿，认为已经是元为单位
                cashflow_value = raw_value
            else:  # 如果较小，认为是万元为单位，需要转换
                cashflow_value = raw_value * 10000
                
            total_cashflow += cashflow_value
            print(f"  {row['end_date'].strftime('%Y-%m-%d')}: "
                  f"{cashflow_value:,.2f}元 ({cashflow_value/100000000:.2f}亿元) "
                  f"[原始值: {raw_value}]")
        
        print("-" * 60)
        print(f"计算得出的TTM现金流: {total_cashflow:,.2f}元 ({total_cashflow/100000000:.2f}亿元)")
        print(f"你提供的TTM现金流: {expected_cashflow_ttm:,.2f}元 ({expected_cashflow_ttm/100000000:.2f}亿元)")
        
        difference = abs(total_cashflow - expected_cashflow_ttm)
        print(f"差异: {difference:,.2f}元 ({difference/100000000:.2f}亿元)")
        
        # 判断差异程度
        if difference / expected_cashflow_ttm < 0.05:  # 差异小于5%
            print("✅ 数据基本吻合！（差异<5%）")
        elif difference / expected_cashflow_ttm < 0.1:  # 差异小于10%
            print("⚠️ 数据基本接近（差异<10%）")
        else:
            print("❌ 数据存在显著差异，可能原因：")
            print("   - 数据源不同（tushare vs 其他数据源）")
            print("   - 计算期间不同（具体的4个季度选择）") 
            print("   - 数据调整/重述")
            print("   - 汇率或单位换算差异")
            
        return total_cashflow
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return None

if __name__ == "__main__":
    print("开始获取格力电器TTM财务数据...")
    print("请确保已正确设置tushare token\n")
    
    # 获取基础TTM数据
    ttm_data = get_gree_ttm_data()
    
    # 获取财务指标中的TTM数据
    indicator_data = get_latest_financial_indicators()
    
    # 验证特定日期的TTM数据
    print("\n" + "="*60)
    verify_specific_ttm_data()
    
    if ttm_data:
        print(f"\n✅ 获取完成！TTM数据汇总:")
        for key, value in ttm_data.items():
            print(f"   {key}: {value:.2f} ({value/10000:.2f}亿元)")