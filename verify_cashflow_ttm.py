#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证现金流TTM计算 - 专门验证格力电器2025/4/30的数据

目标值：43311336869.96（您提供的参考值）
"""

import tushare as ts
import pandas as pd
from datetime import datetime

# 设置tushare token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def verify_gree_cashflow_ttm():
    """验证格力电器在2025年4月30日的现金流TTM"""
    
    ts_code = "000651.SZ"
    target_date = "20250430"
    
    print(f"=== 验证格力电器现金流TTM ===")
    print(f"股票代码: {ts_code}")
    print(f"目标日期: {target_date}")
    print(f"您提供的参考值: 43,311,336,869.96")
    print("="*50)
    
    try:
        # 获取现金流数据
        df = pro.cashflow(
            ts_code=ts_code,
            fields=[
                'ts_code', 'end_date', 'report_type', 'n_cashflow_act', 'ann_date', 'f_ann_date'
            ]
        )
        
        print(f"获取到 {len(df)} 条原始记录")
        
        if df.empty:
            print("未获取到数据")
            return None
        
        # 显示原始数据
        print("\n原始数据前10条:")
        print(df.head(10))
        
        # 转换日期格式
        df['end_date'] = pd.to_datetime(df['end_date'])
        df['ann_date'] = pd.to_datetime(df['ann_date']) if 'ann_date' in df.columns else None
        df['f_ann_date'] = pd.to_datetime(df['f_ann_date']) if 'f_ann_date' in df.columns else None
        
        # 按日期排序
        df = df.sort_values('end_date', ascending=False)
        
        # 去重
        df = df.drop_duplicates(subset=['end_date', 'report_type'], keep='first')
        
        print(f"\n去重后有 {len(df)} 条记录")
        
        # 筛选到2025年4月30日之前的数据
        target_datetime = pd.to_datetime(target_date)
        df_before_target = df[df['end_date'] <= target_datetime]
        
        print(f"截止到 {target_date} 有 {len(df_before_target)} 条记录")
        
        # 显示截止目标日期的最近数据
        print(f"\n截止到 {target_date} 的最近10条记录:")
        print(df_before_target[['end_date', 'report_type', 'n_cashflow_act']].head(10))
        
        # 计算TTM - 方法1：取最近4个季度
        print("\n=== 方法1：取最近4个季度 ===")
        recent_4 = df_before_target.head(4)
        ttm_method1 = recent_4['n_cashflow_act'].fillna(0).sum()
        
        print("最近4个季度数据:")
        for _, row in recent_4.iterrows():
            print(f"  {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
        print(f"TTM (方法1): {ttm_method1:,.2f}")
        
        # 计算TTM - 方法2：严格按季度筛选
        print("\n=== 方法2：严格按季度筛选 ===")
        # 筛选季度末数据（3月、6月、9月、12月）
        df_quarterly = df_before_target[
            df_before_target['end_date'].dt.month.isin([3, 6, 9, 12])
        ].copy()
        
        print(f"季度末数据有 {len(df_quarterly)} 条")
        print("季度末数据:")
        print(df_quarterly[['end_date', 'report_type', 'n_cashflow_act']].head(10))
        
        if len(df_quarterly) >= 4:
            recent_4_quarterly = df_quarterly.head(4)
            ttm_method2 = recent_4_quarterly['n_cashflow_act'].fillna(0).sum()
            
            print("最近4个季度末数据:")
            for _, row in recent_4_quarterly.iterrows():
                print(f"  {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
            print(f"TTM (方法2): {ttm_method2:,.2f}")
        else:
            print("季度末数据不足4条")
            ttm_method2 = None
        
        # 计算TTM - 方法3：查找特定的4个季度
        print("\n=== 方法3：查找2024Q2到2025Q1的数据 ===")
        target_quarters = [
            pd.to_datetime('2025-03-31'),  # 2025Q1
            pd.to_datetime('2024-12-31'),  # 2024Q4
            pd.to_datetime('2024-09-30'),  # 2024Q3
            pd.to_datetime('2024-06-30'),  # 2024Q2
        ]
        
        ttm_method3_data = []
        for quarter_end in target_quarters:
            quarter_data = df_before_target[df_before_target['end_date'] == quarter_end]
            if not quarter_data.empty:
                ttm_method3_data.append(quarter_data.iloc[0])
                print(f"  {quarter_end.strftime('%Y-%m-%d')}: {quarter_data.iloc[0]['n_cashflow_act']:,.2f}")
            else:
                print(f"  {quarter_end.strftime('%Y-%m-%d')}: 未找到数据")
        
        if len(ttm_method3_data) == 4:
            ttm_method3 = sum([row['n_cashflow_act'] for row in ttm_method3_data])
            print(f"TTM (方法3): {ttm_method3:,.2f}")
        else:
            print("无法找到完整的4个季度数据")
            ttm_method3 = None
        
        # 对比结果
        print("\n=== 结果对比 ===")
        target_value = 43311336869.96
        print(f"您提供的参考值: {target_value:,.2f}")
        print(f"方法1结果: {ttm_method1:,.2f} (差异: {abs(ttm_method1 - target_value):,.2f})")
        if ttm_method2:
            print(f"方法2结果: {ttm_method2:,.2f} (差异: {abs(ttm_method2 - target_value):,.2f})")
        if ttm_method3:
            print(f"方法3结果: {ttm_method3:,.2f} (差异: {abs(ttm_method3 - target_value):,.2f})")
        
        return {
            'method1': ttm_method1,
            'method2': ttm_method2,
            'method3': ttm_method3,
            'target': target_value
        }
        
    except Exception as e:
        print(f"验证过程中出错: {str(e)}")
        return None

if __name__ == "__main__":
    result = verify_gree_cashflow_ttm()
