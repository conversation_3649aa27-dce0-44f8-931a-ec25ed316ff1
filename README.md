# 现金流TTM计算器

## 简介

现金流TTM（Trailing Twelve Months）计算器是一个基于tushare数据源的Python工具，用于计算股票过去十二个月的经营活动产生的现金流量之和。

**TTM定义**：过去十二个月（Trailing Twelve Months）的经营活动现金流量净额之和，这是一个重要的财务指标，用于评估公司的现金创造能力。

## 功能特点

- ✅ 从tushare获取实时现金流数据
- ✅ 自动计算现金流TTM
- ✅ 支持单只股票和批量计算
- ✅ 自动去重和数据清洗
- ✅ 结果导出为CSV格式
- ✅ 详细的计算过程展示

## 安装要求

```bash
pip install tushare pandas
```

## 使用方法

### 1. 配置tushare token

在代码中替换您自己的tushare token：

```python
ts.set_token('您的tushare_token')
```

### 2. 单只股票计算

```python
from cashflow_ttm_calculator import calculate_cashflow_ttm

# 计算格力电器的现金流TTM
ttm = calculate_cashflow_ttm('000651.SZ')
print(f"现金流TTM: {ttm:,.2f} 万元")
```

### 3. 批量计算

```python
from cashflow_ttm_calculator import batch_calculate_cashflow_ttm

# 批量计算多只股票
stock_list = ['000651.SZ', '000858.SZ', '002415.SZ']
results = batch_calculate_cashflow_ttm(stock_list)
print(results)
```

### 4. 直接运行

```bash
python cashflow_ttm_calculator.py
```

## 输出示例

### 单只股票结果
```
=== 现金流TTM计算结果 ===
股票代码: 000651.SZ
计算日期: 20250824
最近4个季度的经营活动现金流:
  2025-03-31: 11,001,218,583.01 万元
  2024-12-31: 29,369,250,570.66 万元
  2024-09-30: 12,712,439,743.99 万元
  2024-06-30: 5,122,166,411.40 万元
现金流TTM: 58,205,075,309.06 万元
现金流TTM: 5820507.53 亿元
```

### 批量计算结果
```
  ts_code  cashflow_ttm_万元  cashflow_ttm_亿元 calculation_date
000651.SZ     5.820508e+10     5.820508e+06         20250824
000858.SZ     9.301060e+10     9.301060e+06         20250824
002415.SZ     1.947346e+10     1.947346e+06         20250824
```

## 文件说明

- `cashflow_ttm_calculator.py` - 主程序文件
- `123.py` - 原始测试文件
- `cashflow_ttm_results_*.csv` - 计算结果文件

## 计算逻辑

1. **数据获取**：从tushare获取股票现金流数据
2. **数据清洗**：去除重复记录，按日期排序
3. **季度筛选**：优先使用季报数据（report_type=1）
4. **TTM计算**：取最近4个季度的经营活动现金流量净额求和
5. **结果输出**：显示详细计算过程和最终结果

## 注意事项

- 需要有效的tushare token
- 确保网络连接正常
- 数据来源于tushare，可能存在延迟
- 计算结果单位为万元
- 如果季报数据不足，会使用所有可用数据

## 示例股票代码

- 000651.SZ - 格力电器
- 000858.SZ - 五粮液  
- 002415.SZ - 海康威视

## 技术支持

如有问题，请检查：
1. tushare token是否有效
2. 网络连接是否正常
3. 股票代码格式是否正确（如：000651.SZ）
4. 是否有足够的历史数据（至少4个季度）

---

**免责声明**：本工具仅供学习和研究使用，不构成投资建议。投资有风险，决策需谨慎。
