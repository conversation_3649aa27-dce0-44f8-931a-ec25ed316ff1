#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找正确的现金流TTM计算方法
目标：找到能得出 43,311,336,869.96 的计算方法
"""

import tushare as ts
import pandas as pd
from datetime import datetime

# 设置tushare token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def find_correct_calculation():
    """寻找正确的计算方法"""
    
    ts_code = "000651.SZ"
    target_value = 43311336869.96
    
    print("=== 寻找正确的现金流TTM计算方法 ===")
    print(f"目标值: {target_value:,.2f}")
    print("="*50)
    
    try:
        # 获取现金流数据
        df = pro.cashflow(
            ts_code=ts_code,
            fields=[
                'ts_code', 'end_date', 'report_type', 'n_cashflow_act'
            ]
        )
        
        # 转换日期并排序
        df['end_date'] = pd.to_datetime(df['end_date'])
        df = df.sort_values('end_date', ascending=False)
        df = df.drop_duplicates(subset=['end_date', 'report_type'], keep='first')
        
        print("所有可用的季度数据:")
        for i, (_, row) in enumerate(df.head(12).iterrows()):
            print(f"  {i}: {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
        
        # 尝试各种可能的4个季度组合
        print(f"\n=== 尝试各种4个季度的组合 ===")
        
        best_match = None
        best_diff = float('inf')
        
        # 生成所有可能的4个季度组合
        from itertools import combinations
        
        for combo in combinations(range(min(12, len(df))), 4):
            selected_data = df.iloc[list(combo)]
            combo_sum = selected_data['n_cashflow_act'].sum()
            diff = abs(combo_sum - target_value)
            
            if diff < best_diff:
                best_diff = diff
                best_match = {
                    'combo': combo,
                    'sum': combo_sum,
                    'diff': diff,
                    'data': selected_data
                }
            
            # 只显示差异较小的组合
            if diff < 5000000000:  # 差异小于50亿
                print(f"组合 {combo}: {combo_sum:,.2f} (差异: {diff:,.2f})")
                for idx in combo:
                    row = df.iloc[idx]
                    print(f"    {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
                print()
        
        print(f"=== 最佳匹配 ===")
        if best_match:
            print(f"最佳组合: {best_match['combo']}")
            print(f"计算结果: {best_match['sum']:,.2f}")
            print(f"差异: {best_match['diff']:,.2f}")
            print("包含的季度:")
            for _, row in best_match['data'].iterrows():
                print(f"    {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
        
        # 检查是否是特定的时间范围
        print(f"\n=== 检查特定时间范围 ===")
        
        # 检查2024年5月1日到2025年4月30日的数据
        start_date = pd.to_datetime('2024-05-01')
        end_date = pd.to_datetime('2025-04-30')
        
        df_period = df[(df['end_date'] >= start_date) & (df['end_date'] <= end_date)]
        if not df_period.empty:
            period_sum = df_period['n_cashflow_act'].sum()
            period_diff = abs(period_sum - target_value)
            print(f"2024-05-01到2025-04-30期间数据:")
            for _, row in df_period.iterrows():
                print(f"    {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
            print(f"期间总和: {period_sum:,.2f} (差异: {period_diff:,.2f})")
        
        # 检查是否需要排除某些特定季度
        print(f"\n=== 检查排除特定季度的情况 ===")
        
        # 排除2024Q2，使用2024Q1
        exclude_q2_2024 = df[~((df['end_date'].dt.year == 2024) & (df['end_date'].dt.month == 6))]
        recent_4_exclude_q2 = exclude_q2_2024.head(4)
        exclude_q2_sum = recent_4_exclude_q2['n_cashflow_act'].sum()
        exclude_q2_diff = abs(exclude_q2_sum - target_value)
        
        print(f"排除2024Q2，使用最近4个季度:")
        for _, row in recent_4_exclude_q2.iterrows():
            print(f"    {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
        print(f"总和: {exclude_q2_sum:,.2f} (差异: {exclude_q2_diff:,.2f})")
        
        # 检查是否使用年化数据
        print(f"\n=== 检查年化计算 ===")
        
        # 使用最新的年报数据
        latest_annual = df[df['end_date'].dt.month == 12].iloc[0]
        annual_value = latest_annual['n_cashflow_act']
        annual_diff = abs(annual_value - target_value)
        
        print(f"最新年报数据 ({latest_annual['end_date'].strftime('%Y-%m-%d')}): {annual_value:,.2f}")
        print(f"与目标值差异: {annual_diff:,.2f}")
        
        return best_match
        
    except Exception as e:
        print(f"计算过程中出错: {str(e)}")
        return None

if __name__ == "__main__":
    result = find_correct_calculation()
