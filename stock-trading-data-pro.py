import tushare as ts
import pandas as pd

# Make sure you have tushare and pandas installed:
# pip install tushare pandas

# Set your Tushare token here
token = '2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211'
ts.set_token(token)

# Initialize the Tushare Pro API
pro = ts.pro_api()

# The stock code for sz000002 in Tushare is 000002.SZ
stock_code = '000002.SZ'
output_filename = 'sz000002.csv'

print(f"Fetching daily data for {stock_code}...")

# Fetch the daily historical data
# The 'daily' interface provides the fields you requested.
df = pro.daily(ts_code=stock_code)

# Fetch daily basic data for market value
df_basic = pro.daily_basic(ts_code=stock_code, fields='ts_code,trade_date,circ_mv,total_mv')

# Fetch financial indicators for TTM data
print("Fetching financial indicators data...")
df_fina = pro.fina_indicator(ts_code=stock_code, fields='ts_code,end_date,profit_ttm,cf_sales_ttm')
# Rename end_date to trade_date for merging
df_fina = df_fina.rename(columns={'end_date': 'trade_date'})

# Merge the dataframes
if not df.empty and not df_basic.empty:
    df = pd.merge(df, df_basic, on=['ts_code', 'trade_date'])
    # Convert amount unit from thousand Yuan to Yuan
    if 'amount' in df.columns:
        df['amount'] = df['amount'] * 1000
    # Convert market value unit from ten thousand Yuan to Yuan
    if 'circ_mv' in df.columns:
        df['circ_mv'] = df['circ_mv'] * 10000
    if 'total_mv' in df.columns:
        df['total_mv'] = df['total_mv'] * 10000

# Fetch stock basic information to get the name
stock_basics = pro.stock_basic(ts_code=stock_code, fields='ts_code,name')
stock_name = ''
if not stock_basics.empty:
    stock_name = stock_basics.iloc[0]['name']

# Save the DataFrame to a CSV file
if not df.empty:
    # Select and rename the required columns
    df_selected = df[['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pre_close', 'vol', 'amount', 'circ_mv', 'total_mv']].copy()
    
    # Reformat stock code from '000002.SZ' to 'sz000002'
    df_selected['ts_code'] = df_selected['ts_code'].apply(lambda x: f"{x.split('.')[1].lower()}{x.split('.')[0]}")

    if stock_name:
        df_selected.insert(1, 'name', stock_name)

    column_mapping = {
        'ts_code': '股票代码',
        'name': '股票名称',
        'trade_date': '交易日期',
        'open': '开盘价',
        'high': '最高价',
        'low': '最低价',
        'close': '收盘价',
        'pre_close': '前收盘价',
        'vol': '成交量',
        'amount': '成交额',
        'circ_mv': '流通市值',
        'total_mv': '总市值'
    }
    df_selected.rename(columns=column_mapping, inplace=True)

    df_selected.to_csv(output_filename, index=False, encoding='utf-8-sig')
    print(f"Data successfully saved to {output_filename}")
else:
    print(f"No data returned for {stock_code}. Please check the stock code and your Tushare permissions.")
