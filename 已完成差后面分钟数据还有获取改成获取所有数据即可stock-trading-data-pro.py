import tushare as ts
import pandas as pd
import numpy as np

# 设置Tushare API的token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

# 格力电器的股票代码
gree_code = '000651.SZ'

# 开始日期和结束日期
start_date = '20200622'  # 2020年6月22日
end_date = '20250401'    # 2025年4月1日（注意：未来日期将只返回到当前日期为止的数据）

def calculate_cashflow_ttm(ts_code, start_date, end_date):
    """计算经营活动现金流TTM，使用您提供的方法"""
    # 获取现金流量表数据
    # 获取更早的开始日期，确保有足够的数据计算TTM
    cf_start_date = str(int(start_date[:4]) - 1) + start_date[4:]
    df = pro.cashflow(ts_code=ts_code, start_date=cf_start_date, end_date=end_date)
    
    # 确保按日期和报表类型正确排序
    df = df.sort_values(['end_date', 'report_type'])
    
    # 提取年份和季度信息，用于后续处理
    df['year'] = df['end_date'].str[:4].astype(int)
    df['quarter'] = df['end_date'].str[4:6].apply(lambda x: (int(x)+2)//3)  # 将月份转换为季度
    
    # 根据报表类型进行处理
    # 优先选择合并报表(report_type=1)或调整后的合并报表(report_type=4)
    def select_best_report(group):
        # 对于每个报告期末，选择最合适的报表
        if 1 in group['report_type'].values:  # 首选合并报表
            return group[group['report_type'] == 1]
        elif 4 in group['report_type'].values:  # 其次选择调整后的合并报表
            return group[group['report_type'] == 4]
        else:
            return group.iloc[0:1]  # 如果都没有，选择第一个
    
    # 按报告期末分组，选择最合适的报表
    selected_reports = []
    for name, group in df.groupby(['end_date']):
        selected_reports.append(select_best_report(group))
    
    processed_df = pd.concat(selected_reports)
    
    # 处理季度数据（从累计数据转为单季度数据）
    def get_quarterly_data(df):
        df = df.sort_values(['year', 'quarter'])
        
        # 现金流相关指标
        cols = ['n_cashflow_act', 'free_cashflow', 'c_paid_to_for_empl', 
                'n_cashflow_inv_act', 'n_cash_flows_fnc_act', 'im_net_cashflow_oper_act']
        
        # 创建季度数据的副本
        quarterly_df = df.copy()
        
        for year in quarterly_df['year'].unique():
            year_data = quarterly_df[quarterly_df['year'] == year].copy()
            
            if len(year_data) > 0:
                # 第一季度保持不变
                first_quarter = year_data[year_data['quarter'] == 1]
                if not first_quarter.empty:
                    for col in cols:
                        if col in df.columns:
                            quarterly_df.loc[first_quarter.index, f'{col}_q'] = quarterly_df.loc[first_quarter.index, col]
                
                # 处理后续季度
                for q in [2, 3, 4]:
                    current_q = year_data[year_data['quarter'] == q]
                    prev_q = year_data[year_data['quarter'] == q-1]
                    
                    if not current_q.empty and not prev_q.empty:
                        for col in cols:
                            if col in df.columns:
                                current_val = current_q[col].iloc[0]
                                prev_val = prev_q[col].iloc[0]
                                
                                # 如果两者都不是NaN，则计算差值
                                if pd.notna(current_val) and pd.notna(prev_val):
                                    quarterly_df.loc[current_q.index, f'{col}_q'] = current_val - prev_val
                                else:
                                    quarterly_df.loc[current_q.index, f'{col}_q'] = np.nan
        
        return quarterly_df
    
    # 计算季度数据
    quarterly_df = get_quarterly_data(processed_df)
    
    # 计算TTM值（滚动四个季度）
    def calculate_ttm(df):
        df = df.sort_values(['end_date'])
        cols = ['n_cashflow_act', 'free_cashflow', 'c_paid_to_for_empl', 
                'n_cashflow_inv_act', 'n_cash_flows_fnc_act', 'im_net_cashflow_oper_act']
        
        # 对每个指标计算TTM
        for col in cols:
            q_col = f'{col}_q'
            if q_col in df.columns:
                # 对每个报告期，查找前四个季度并求和
                for i in range(len(df)):
                    current_row = df.iloc[i]
                    current_year = current_row['year']
                    current_quarter = current_row['quarter']
                    
                    # 构建前四个季度的条件
                    quarters = []
                    for j in range(4):
                        if current_quarter - j > 0:
                            quarters.append((current_year, current_quarter - j))
                        else:
                            quarters.append((current_year - 1, current_quarter - j + 4))
                    
                    # 查找这四个季度的数据
                    quarter_values = []
                    for year, quarter in quarters:
                        quarter_data = df[(df['year'] == year) & (df['quarter'] == quarter)]
                        if not quarter_data.empty and q_col in quarter_data.columns:
                            val = quarter_data[q_col].iloc[0]
                            if pd.notna(val):
                                quarter_values.append(val)
                    
                    # 如果有完整的四个季度数据，则计算TTM
                    if len(quarter_values) == 4:
                        df.loc[df.index[i], f'{col}_ttm'] = sum(quarter_values)
                    else:
                        df.loc[df.index[i], f'{col}_ttm'] = np.nan
        
        return df
    
    # 计算TTM值
    ttm_df = calculate_ttm(quarterly_df)
    
    # 选择所需列
    output_cols = ['ts_code', 'ann_date', 'end_date']
    ttm_cols = ['n_cashflow_act_ttm']
    
    for col in ttm_cols:
        if col in ttm_df.columns:
            output_cols.append(col)
    
    # 提取经营活动现金流TTM结果
    result_df = ttm_df[output_cols].dropna(subset=['n_cashflow_act_ttm'], how='all')
    result_df = result_df.sort_values('end_date', ascending=False)
    
    return result_df

def get_balancesheet_data(ts_code, start_date, end_date):
    """从资产负债表获取净资产数据"""
    # 获取更早的开始日期，确保有足够数据
    bs_start_date = str(int(start_date[:4]) - 2) + start_date[4:]
    
    try:
        # 获取资产负债表数据
        print("获取资产负债表数据...")
        bs_data = pro.balancesheet(ts_code=ts_code, start_date=bs_start_date, end_date=end_date,
                                 fields='ts_code,ann_date,end_date,total_hldr_eqy_exc_min_int,total_liab,report_type')
        
        if not bs_data.empty:
            print(f"成功获取到 {len(bs_data)} 条资产负债表数据")
            
            # 显示一些数据示例
            print("资产负债表数据示例:")
            print(bs_data[['end_date', 'ann_date', 'total_hldr_eqy_exc_min_int', 'total_liab', 'report_type']].head())
            
            # 确保按日期和报表类型正确排序
            bs_data = bs_data.sort_values(['end_date', 'report_type'])
            
            # 优先选择合并报表
            def select_best_report(group):
                if 1 in group['report_type'].values:  # 首选合并报表
                    return group[group['report_type'] == 1]
                elif 4 in group['report_type'].values:  # 其次选择调整后的合并报表
                    return group[group['report_type'] == 4]
                else:
                    return group.iloc[0:1]  # 如果都没有，选择第一个
            
            # 按报告期末分组，选择最合适的报表
            selected_reports = []
            for name, group in bs_data.groupby(['end_date']):
                selected_reports.append(select_best_report(group))
            
            processed_bs = pd.concat(selected_reports)
            
            # 确保日期格式一致
            processed_bs['ann_date'] = processed_bs['ann_date'].astype(str)
            processed_bs['end_date'] = processed_bs['end_date'].astype(str)
            
            # 创建净资产映射字典，同时以公告日期和报告期末为键
            net_assets_dict = {}
            total_liab_dict = {}
            
            for _, row in processed_bs.iterrows():
                if pd.notna(row['total_hldr_eqy_exc_min_int']):
                    net_assets_dict[row['ann_date']] = row['total_hldr_eqy_exc_min_int']  # 万元
                    net_assets_dict[row['end_date']] = row['total_hldr_eqy_exc_min_int']  # 万元
                
                if pd.notna(row['total_liab']):
                    total_liab_dict[row['ann_date']] = row['total_liab']  # 万元
                    total_liab_dict[row['end_date']] = row['total_liab']  # 万元
            
            return net_assets_dict, total_liab_dict
        else:
            print("无法获取资产负债表数据")
            return {}, {}
    except Exception as e:
        print(f"获取资产负债表数据时出错: {e}")
        return {}, {}

def get_express_data(ts_code, start_date, end_date):
    """从业绩快报获取总资产数据"""
    # 获取更早的开始日期，确保有足够数据
    exp_start_date = str(int(start_date[:4]) - 2) + start_date[4:]
    
    try:
        # 获取业绩快报数据
        print("获取业绩快报数据...")
        express_data = pro.express(ts_code=ts_code, start_date=exp_start_date, end_date=end_date,
                                 fields='ts_code,ann_date,end_date,total_assets')
        
        if not express_data.empty:
            print(f"成功获取到 {len(express_data)} 条业绩快报数据")
            
            # 显示一些数据示例
            print("业绩快报数据示例:")
            print(express_data[['end_date', 'ann_date', 'total_assets']].head())
            
            # 确保按日期排序
            express_data = express_data.sort_values(['end_date', 'ann_date'])
            
            # 确保日期格式一致
            express_data['ann_date'] = express_data['ann_date'].astype(str)
            express_data['end_date'] = express_data['end_date'].astype(str)
            
            # 创建总资产映射字典，同时以公告日期和报告期末为键
            total_assets_dict = {}
            
            for _, row in express_data.iterrows():
                if pd.notna(row['total_assets']):
                    total_assets_dict[row['ann_date']] = row['total_assets']  # 元
                    total_assets_dict[row['end_date']] = row['total_assets']  # 元
            
            return total_assets_dict
        else:
            print("无法获取业绩快报数据")
            return {}
    except Exception as e:
        print(f"获取业绩快报数据时出错: {e}")
        return {}

def calculate_net_assets_from_market_pb(ts_code, start_date, end_date):
    """使用市值和市净率计算净资产"""
    try:
        # 获取市净率数据
        print("获取市净率数据...")
        pb_data = pro.daily_basic(ts_code=ts_code, start_date=start_date, end_date=end_date,
                                fields='ts_code,trade_date,pb,total_mv')
        
        if not pb_data.empty:
            print(f"成功获取到 {len(pb_data)} 条市净率数据")
            
            # 计算净资产 = 总市值 / 市净率
            # total_mv单位为万元，结果也为万元
            pb_data['net_assets'] = pb_data['total_mv'] / pb_data['pb']  # 万元
            
            # 处理可能的除零错误或无效值
            pb_data['net_assets'].replace([np.inf, -np.inf], np.nan, inplace=True)
            
            # 创建净资产映射字典
            net_assets_dict = {}
            for _, row in pb_data.iterrows():
                if pd.notna(row['net_assets']):
                    net_assets_dict[row['trade_date']] = row['net_assets']
            
            return net_assets_dict
        else:
            print("无法获取市净率数据")
            return {}
    except Exception as e:
        print(f"计算净资产时出错: {e}")
        return {}

def get_index_components():
    """获取各大指数的成分股信息"""
    # 4. 获取各大指数的成分股信息
    indices = {
        '沪深300成分股': '000300.SH',
        '上证50成分股': '000016.SH',
        '中证500成分股': '000905.SH',
        '中证1000成分股': '000852.SH',
        '中证2000成分股': '932000.CSI',
        '创业板指成分股': '399006.SZ'
    }

    # 获取每个指数的成分股
    index_components = {}
    for index_name, index_code in indices.items():
        try:
            df = pro.index_weight(index_code=index_code, start_date=start_date)
            if not df.empty:
                index_components[index_name] = df['con_code'].tolist()
            else:
                index_components[index_name] = []
        except:
            index_components[index_name] = []
            
    return index_components

def get_industry_classification(ts_code):
    """获取申万行业分类信息"""
    # 获取申万行业分类信息
    try:
        sw_industry = pro.index_member_all(ts_code=ts_code)
        if not sw_industry.empty:
            sw_l1_name = sw_industry.iloc[0]['l1_name']  # 新版申万一级行业名称
            sw_l2_name = sw_industry.iloc[0]['l2_name']  # 新版申万二级行业名称
            sw_l3_name = sw_industry.iloc[0]['l3_name']  # 新版申万三级行业名称
        else:
            sw_l1_name = ''
            sw_l2_name = ''
            sw_l3_name = ''
    except:
        sw_l1_name = ''
        sw_l2_name = ''
        sw_l3_name = ''
        
    return sw_l1_name, sw_l2_name, sw_l3_name

def get_comprehensive_data(ts_code=gree_code, start_date=start_date, end_date=end_date):
    try:
        # 获取股票名称
        stock_info = pro.stock_basic(ts_code=ts_code, fields='ts_code,name')
        stock_name = stock_info['name'].values[0] if not stock_info.empty else '格力电器'
        
        # 1. 获取日历日期列表（交易日）
        print("获取交易日历...")
        trade_cal = pro.trade_cal(exchange='SSE', start_date=start_date, end_date=end_date)
        trade_dates = trade_cal[trade_cal['is_open'] == 1]['cal_date'].tolist()
        
        # 2. 获取季度净利润数据
        print("获取季度净利润数据...")
        quarterly_profit = pro.income(ts_code=ts_code, 
                                     start_date=start_date, 
                                     end_date=end_date,
                                     report_type='2',
                                     fields='ts_code,ann_date,end_date,n_income')
        
        # 3. 获取资金流向数据
        print("获取资金流向数据...")
        moneyflow = pro.moneyflow(ts_code=ts_code, start_date=start_date, end_date=end_date)
        
        # 获取各大指数的成分股信息
        print("获取指数成分股信息...")
        index_components = get_index_components()
        
        # 获取申万行业分类信息
        print("获取申万行业分类信息...")
        sw_l1_name, sw_l2_name, sw_l3_name = get_industry_classification(ts_code)
        
        # 获取日线行情数据
        print("获取日线行情数据...")
        daily_data = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)
        
        # 获取每日指标（包含流通市值、总市值、市盈率TTM）
        print("获取每日指标数据...")
        daily_basic = pro.daily_basic(ts_code=ts_code, start_date=start_date, end_date=end_date,
                                    fields='ts_code,trade_date,circ_mv,total_mv,pe_ttm,pb')
        
        # 合并数据
        if not daily_data.empty and not daily_basic.empty:
            print("合并日线行情和每日指标数据...")
            merged_data = pd.merge(daily_data, daily_basic, on=['ts_code', 'trade_date'])
            
            # 添加股票名称
            merged_data['name'] = stock_name
            
            # 单位转换
            # 将流通市值和总市值从万元转为元
            merged_data['circ_mv'] = merged_data['circ_mv'] * 10000
            merged_data['total_mv'] = merged_data['total_mv'] * 10000
            
            # 将成交量从手转为股（1手=100股）
            merged_data['vol'] = merged_data['vol'] * 100
            
            # 将成交额从千元转为元
            merged_data['amount'] = merged_data['amount'] * 1000
            
            # 计算净利润TTM - 使用总市值/市盈率TTM
            merged_data['net_profit_ttm'] = merged_data['total_mv'] / merged_data['pe_ttm']
            
            # 处理可能的除零错误或无效值
            merged_data['net_profit_ttm'].replace([np.inf, -np.inf], np.nan, inplace=True)
            
            # ================ 获取经营活动现金流量净额TTM ================
            print("计算经营活动现金流TTM...")
            
            # 使用提供的方法计算经营活动现金流TTM
            cashflow_ttm = calculate_cashflow_ttm(ts_code, start_date, end_date)
            
            if not cashflow_ttm.empty:
                print(f"成功计算现金流TTM，共 {len(cashflow_ttm)} 条记录")
                
                # 确保日期格式一致
                cashflow_ttm['ann_date'] = cashflow_ttm['ann_date'].astype(str)
                
                # 创建TTM映射字典: 以公告日期为键，TTM值为值
                cf_ttm_dict = dict(zip(cashflow_ttm['ann_date'], cashflow_ttm['n_cashflow_act_ttm']))
                
                # 为每个交易日分配现金流TTM
                merged_data['n_cashflow_act_ttm'] = None
                
                for i, row in merged_data.iterrows():
                    trade_date = row['trade_date']
                    # 找到小于等于交易日期的最大公告日期
                    valid_dates = [d for d in cf_ttm_dict.keys() if d <= trade_date]
                    if valid_dates:
                        latest_valid = max(valid_dates)
                        merged_data.at[i, 'n_cashflow_act_ttm'] = cf_ttm_dict[latest_valid]
                
                print(f"成功映射现金流TTM数据，非空值数量: {merged_data['n_cashflow_act_ttm'].count()}")
                
            else:
                print("无法计算现金流TTM，将设为空值")
                merged_data['n_cashflow_act_ttm'] = None
            
            # ================ 获取净资产和总负债 ================
            print("尝试获取净资产和总负债数据...")
            
            # 从资产负债表获取净资产和总负债数据
            net_assets_dict, total_liab_dict = get_balancesheet_data(ts_code, start_date, end_date)
            
            if net_assets_dict:
                print(f"成功获取净资产数据，共 {len(net_assets_dict)} 条记录")
                
                merged_data['net_assets'] = None
                
                for i, row in merged_data.iterrows():
                    trade_date = row['trade_date']
                    # 找到小于等于交易日期的最大公告日期
                    valid_dates = [d for d in net_assets_dict.keys() if d <= trade_date]
                    if valid_dates:
                        latest_valid = max(valid_dates)
                        merged_data.at[i, 'net_assets'] = net_assets_dict[latest_valid]
                
                print(f"成功映射净资产数据，非空值数量: {merged_data['net_assets'].count()}")
            else:
                print("无法获取净资产数据，使用市值/市净率计算...")
                pb_assets_dict = calculate_net_assets_from_market_pb(ts_code, start_date, end_date)
                
                if pb_assets_dict:
                    merged_data['net_assets'] = None
                    for i, row in merged_data.iterrows():
                        trade_date = row['trade_date']
                        if trade_date in pb_assets_dict:
                            merged_data.at[i, 'net_assets'] = pb_assets_dict[trade_date]
                else:
                    merged_data['net_assets'] = None
            
            if total_liab_dict:
                print(f"成功获取总负债数据，共 {len(total_liab_dict)} 条记录")
                
                merged_data['total_liab'] = None
                
                for i, row in merged_data.iterrows():
                    trade_date = row['trade_date']
                    # 找到小于等于交易日期的最大公告日期
                    valid_dates = [d for d in total_liab_dict.keys() if d <= trade_date]
                    if valid_dates:
                        latest_valid = max(valid_dates)
                        merged_data.at[i, 'total_liab'] = total_liab_dict[latest_valid]
                
                print(f"成功映射总负债数据，非空值数量: {merged_data['total_liab'].count()}")
            else:
                print("无法获取总负债数据，将设为空值")
                merged_data['total_liab'] = None
            
            # ================ 获取总资产 ================
            print("尝试获取总资产数据...")
            
            # 从业绩快报获取总资产数据
            total_assets_dict = get_express_data(ts_code, start_date, end_date)
            
            if total_assets_dict:
                print(f"成功获取总资产数据，共 {len(total_assets_dict)} 条记录")
                
                merged_data['total_assets'] = None
                
                for i, row in merged_data.iterrows():
                    trade_date = row['trade_date']
                    # 找到小于等于交易日期的最大公告日期
                    valid_dates = [d for d in total_assets_dict.keys() if d <= trade_date]
                    if valid_dates:
                        latest_valid = max(valid_dates)
                        merged_data.at[i, 'total_assets'] = total_assets_dict[latest_valid]
                
                print(f"成功映射总资产数据，非空值数量: {merged_data['total_assets'].count()}")
            else:
                # 如果业绩快报没有总资产数据，尝试用净资产+总负债计算
                print("无法从业绩快报获取总资产数据，尝试用净资产+总负债计算...")
                
                if 'net_assets' in merged_data.columns and 'total_liab' in merged_data.columns:
                    # 如果同时有净资产和总负债数据，计算总资产
                    merged_data['total_assets'] = merged_data.apply(
                        lambda row: row['net_assets'] + row['total_liab'] if pd.notna(row['net_assets']) and pd.notna(row['total_liab']) else None, 
                        axis=1
                    )
                    print(f"已计算总资产数据，非空值数量: {merged_data['total_assets'].count()}")
                else:
                    print("无法计算总资产数据，将设为空值")
                    merged_data['total_assets'] = None
            
            # 处理资金流向数据和指数成分股数据
            print("处理资金流向和指数成分股数据...")
            
            # 为每个交易日创建一个字典
            daily_data_dict = {}
            latest_profit = None
            latest_profit_end_date = None

            for date in trade_dates:
                # 检查该日期是否有新的季报发布
                if not quarterly_profit[quarterly_profit['ann_date'] <= date].empty:
                    # 获取该日期前最新的季报净利润
                    latest_report = quarterly_profit[quarterly_profit['ann_date'] <= date].sort_values('ann_date', ascending=False).iloc[0]
                    latest_profit = latest_report['n_income']
                    latest_profit_end_date = latest_report['end_date']
                
                # 如果有净利润数据，添加到每日数据中
                if latest_profit is not None:
                    daily_data_dict[date] = {
                        'latest_quarter_end': latest_profit_end_date,
                        '净利润(当季)': latest_profit
                    }
                    
                    # 添加指数成分股信息
                    for index_name in index_components.keys():
                        daily_data_dict[date][index_name] = 'Y' if ts_code in index_components[index_name] else ''
                    
                    # 添加申万行业分类信息
                    daily_data_dict[date]['新版申万一级行业名称'] = sw_l1_name
                    daily_data_dict[date]['新版申万二级行业名称'] = sw_l2_name
                    daily_data_dict[date]['新版申万三级行业名称'] = sw_l3_name
            
            # 添加资金流向数据
            for _, row in moneyflow.iterrows():
                date = row['trade_date']
                if date in daily_data_dict:
                    daily_data_dict[date]['中户资金买入额'] = row['buy_md_amount']
                    daily_data_dict[date]['中户资金卖出额'] = row['sell_md_amount']
                    daily_data_dict[date]['大户资金买入额'] = row['buy_lg_amount']
                    daily_data_dict[date]['大户资金卖出额'] = row['sell_lg_amount']
                    daily_data_dict[date]['散户资金买入额'] = row['buy_sm_amount']
                    daily_data_dict[date]['散户资金卖出额'] = row['sell_sm_amount']
                    daily_data_dict[date]['机构资金买入额'] = row['buy_elg_amount']
                    daily_data_dict[date]['机构资金卖出额'] = row['sell_elg_amount']
            
            # 将字典数据添加到merged_data
            for i, row in merged_data.iterrows():
                date = row['trade_date']
                if date in daily_data_dict:
                    for key, value in daily_data_dict[date].items():
                        merged_data.at[i, key] = value
            
            # 去掉pe_ttm和pb列，不需要输出
            if 'pe_ttm' in merged_data.columns:
                merged_data.drop('pe_ttm', axis=1, inplace=True)
            if 'pb' in merged_data.columns:
                merged_data.drop('pb', axis=1, inplace=True)
            
            # 重命名列
            column_mapping = {
                'ts_code': '股票代码',
                'name': '股票名称', 
                'trade_date': '交易日期', 
                'open': '开盘价', 
                'high': '最高价', 
                'low': '最低价',
                'close': '收盘价', 
                'pre_close': '前收盘价', 
                'vol': '成交量', 
                'amount': '成交额', 
                'circ_mv': '流通市值', 
                'total_mv': '总市值',
                'net_profit_ttm': '净利润TTM', 
                'n_cashflow_act_ttm': '现金流TTM', 
                'net_assets': '净资产', 
                'total_assets': '总资产', 
                'total_liab': '总负债'
            }
            
            # 应用重命名
            for old_col, new_col in column_mapping.items():
                if old_col in merged_data.columns:
                    merged_data.rename(columns={old_col: new_col}, inplace=True)
            
            # 按照要求排序列
            ordered_columns = [
                '股票代码', '股票名称', '交易日期', '开盘价', '最高价', '最低价', '收盘价', '前收盘价',
                '成交量', '成交额', '流通市值', '总市值', '净利润TTM', '现金流TTM', '净资产', '总资产', 
                '总负债', '净利润(当季)', '中户资金买入额', '中户资金卖出额', '大户资金买入额', 
                '大户资金卖出额', '散户资金买入额', '散户资金卖出额', '机构资金买入额', '机构资金卖出额',
                '沪深300成分股', '上证50成分股', '中证500成分股', '中证1000成分股', '中证2000成分股', 
                '创业板指成分股', '新版申万一级行业名称', '新版申万二级行业名称', '新版申万三级行业名称'
            ]
            
            # 添加缺失的列
            for col in ordered_columns:
                if col not in merged_data.columns:
                    merged_data[col] = None
            
            # 按指定顺序重排列
            merged_data = merged_data[ordered_columns]
            
            # 保存为CSV
            csv_filename = f'格力电器综合数据_{start_date}_{end_date}.csv'
            merged_data.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"格力电器综合数据已保存至 {csv_filename}")
            print(f"共获取到 {len(merged_data)} 条交易记录")
            
            return merged_data
            
        else:
            print("没有获取到格力电器的数据")
            return None
            
    except Exception as e:
        print(f"获取格力电器数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return None

# 运行函数获取格力电器综合数据
if __name__ == "__main__":
    get_comprehensive_data()