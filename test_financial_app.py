#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试财务应用的基本功能
"""

import sys
import os
import pandas as pd

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入是否正常"""
    try:
        import tushare as ts
        import pandas as pd
        import streamlit as st
        from datetime import datetime
        import warnings
        import time
        import os
        print("✅ 所有依赖包导入成功")
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_data_structures():
    """测试数据结构定义"""
    try:
        from financial_app import variables_dict, variables_dict_2, variables_dict_3
        
        print(f"✅ variables_dict 包含 {len(variables_dict)} 个字段")
        print(f"✅ variables_dict_2 包含 {len(variables_dict_2)} 个字段")
        print(f"✅ variables_dict_3 包含 {len(variables_dict_3)} 个字段")
        
        # 检查关键字段
        key_fields = ["ts_code", "ann_date", "end_date"]
        for field in key_fields:
            if field in variables_dict:
                print(f"✅ 关键字段 {field} 存在于 variables_dict")
            else:
                print(f"⚠️ 关键字段 {field} 不存在于 variables_dict")
        
        return True
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False

def test_utility_functions():
    """测试工具函数"""
    try:
        from financial_app import get_annual_data
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '报告期': ['20231231', '20230930', '20230630', '20230331', '20221231'],
            '营业收入': [1000, 800, 600, 400, 900]
        })
        
        annual_data = get_annual_data(test_data)
        
        if len(annual_data) == 2:  # 应该有2条年度数据
            print("✅ get_annual_data 函数工作正常")
            return True
        else:
            print(f"⚠️ get_annual_data 返回了 {len(annual_data)} 条记录，预期2条")
            return False
            
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False

def test_financial_analysis():
    """测试财务分析函数"""
    try:
        from financial_app import Financial_Data_analysis
        
        # 创建测试财务数据
        test_financial_data = pd.DataFrame({
            'TS股票代码': ['000001.SZ', '000001.SZ'],
            '报告期': ['20231231', '20221231'],
            '净利润': [1000, 800],
            '股东权益合计(含少数股东权益)': [10000, 9000],
            '资产总计': [50000, 45000],
            '营业收入': [20000, 18000],
            '应收账款': [2000, 1800],
            '负债合计': [40000, 36000],
            '货币资金': [5000, 4500],
            '流动负债合计': [15000, 13500],
            '流动资产合计': [25000, 22500],
            '营业总收入': [20000, 18000],
            '营业总成本': [15000, 13500],
            '营业利润': [3000, 2700],
            '存货': [3000, 2700]
        })
        
        result = Financial_Data_analysis(test_financial_data)
        
        if not result.empty:
            print("✅ Financial_Data_analysis 函数工作正常")
            print(f"   计算出 {len(result.columns)} 个指标")
            return True
        else:
            print("⚠️ Financial_Data_analysis 返回空结果")
            return False
            
    except Exception as e:
        print(f"❌ 财务分析函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试财务应用...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("数据结构测试", test_data_structures),
        ("工具函数测试", test_utility_functions),
        ("财务分析测试", test_financial_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        try:
            if test_func():
                passed += 1
            else:
                print(f"   {test_name} 未完全通过")
        except Exception as e:
            print(f"   ❌ {test_name} 执行出错: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用基本功能正常")
    else:
        print("⚠️ 部分测试未通过，请检查相关功能")
    
    print("\n💡 使用建议:")
    print("1. 确保已安装所有依赖包: pip install tushare pandas streamlit openpyxl")
    print("2. 获取Tushare API密钥: https://tushare.pro")
    print("3. 运行应用: streamlit run financial_app.py")

if __name__ == "__main__":
    main()
