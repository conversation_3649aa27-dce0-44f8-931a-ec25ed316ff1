#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试现金流数据单位和计算方法
"""

import tushare as ts
import pandas as pd
from datetime import datetime

# 设置tushare token
ts.set_token('2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211')
pro = ts.pro_api()

def debug_cashflow_units():
    """调试现金流数据的单位和计算方法"""
    
    ts_code = "000651.SZ"
    
    print("=== 调试现金流数据单位 ===")
    
    try:
        # 获取现金流数据
        df = pro.cashflow(
            ts_code=ts_code,
            fields=[
                'ts_code', 'end_date', 'report_type', 'n_cashflow_act'
            ]
        )
        
        # 转换日期并排序
        df['end_date'] = pd.to_datetime(df['end_date'])
        df = df.sort_values('end_date', ascending=False)
        df = df.drop_duplicates(subset=['end_date', 'report_type'], keep='first')
        
        # 获取最近4个季度
        recent_4 = df.head(4)
        
        print("最近4个季度的原始数据:")
        for _, row in recent_4.iterrows():
            print(f"  {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']}")
        
        # 计算不同单位的结果
        ttm_original = recent_4['n_cashflow_act'].sum()
        
        print(f"\n=== 不同单位的计算结果 ===")
        print(f"原始数值求和: {ttm_original}")
        print(f"如果原始单位是元: {ttm_original:,.2f} 元")
        print(f"如果原始单位是万元: {ttm_original:,.2f} 万元")
        print(f"如果原始单位是万元，转换为元: {ttm_original * 10000:,.2f} 元")
        
        print(f"\n=== 与目标值对比 ===")
        target = 43311336869.96
        print(f"目标值: {target:,.2f}")
        
        # 检查各种可能的单位转换
        scenarios = [
            ("原始数值", ttm_original),
            ("原始数值/10000", ttm_original / 10000),
            ("原始数值*10000", ttm_original * 10000),
            ("原始数值/100", ttm_original / 100),
        ]
        
        for name, value in scenarios:
            diff = abs(value - target)
            print(f"{name}: {value:,.2f} (差异: {diff:,.2f})")
        
        # 检查是否需要特定的季度组合
        print(f"\n=== 检查其他可能的季度组合 ===")
        
        # 尝试不同的季度组合
        all_quarters = df.head(8)  # 取更多数据
        print("更多季度数据:")
        for i, (_, row) in enumerate(all_quarters.iterrows()):
            print(f"  {i}: {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
        
        # 尝试不同的4个季度组合
        combinations = [
            [0, 1, 2, 3],  # 最近4个
            [0, 1, 2, 4],  # 跳过第4个，取第5个
            [0, 2, 3, 4],  # 跳过第2个
        ]
        
        for i, combo in enumerate(combinations):
            if max(combo) < len(all_quarters):
                selected = all_quarters.iloc[combo]
                combo_sum = selected['n_cashflow_act'].sum()
                diff = abs(combo_sum - target)
                print(f"组合{i+1} {combo}: {combo_sum:,.2f} (差异: {diff:,.2f})")
                
                print(f"  包含季度:")
                for idx in combo:
                    row = all_quarters.iloc[idx]
                    print(f"    {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
        
        # 检查是否需要查看年报数据
        print(f"\n=== 检查年报数据 ===")
        df_annual = df[df['end_date'].dt.month == 12].head(4)
        if not df_annual.empty:
            print("最近的年报数据:")
            for _, row in df_annual.iterrows():
                print(f"  {row['end_date'].strftime('%Y-%m-%d')}: {row['n_cashflow_act']:,.2f}")
        
        return ttm_original
        
    except Exception as e:
        print(f"调试过程中出错: {str(e)}")
        return None

if __name__ == "__main__":
    result = debug_cashflow_units()
